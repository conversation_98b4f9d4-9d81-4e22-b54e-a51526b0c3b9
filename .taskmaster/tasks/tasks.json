{"master": {"tasks": [{"id": 13, "title": "Database Migration and Schema Setup", "description": "Create the org_phone_lines table with proper constraints, indexes, and Row Level Security policies, then migrate existing phone line data from the orgs table", "details": "Create migration file `migrations/20250709134932_add_org_phone_lines_table.sql` with the complete schema including: SERIAL PRIMARY KEY, foreign key to orgs table, line_type field, Twilio integration fields (twilio_receiving_number, twilio_number_sid, twiml_app_sid), is_primary_line boolean, timestamps, unique constraints on twilio_receiving_number and org_id+external_forwarding_number combination. Add performance indexes on twilio_receiving_number, org_id, org_id+line_type, and org_id+is_primary_line. Implement RLS policies following Hero Core patterns with org_phone_lines_organization_access and org_phone_lines_meta_org_access policies. Execute data migration to move existing phone line data from orgs table to org_phone_lines as 'general' type with is_primary_line=true. Clean up orgs table by dropping migrated columns: twilio_number, twilio_number_sid, twiml_app_sid, forwarded_from_number, call_forwarding_type, is_call_forwarding_enabled, sip_uri.", "testStrategy": "Verify table creation with correct schema, validate all constraints and indexes are properly created, test RLS policies with different org contexts, confirm data migration preserves all existing phone line data without loss, validate cleanup removes only intended columns from orgs table, test rollback procedures work correctly", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create org_phone_lines table schema and constraints", "description": "Create the complete database schema for the org_phone_lines table with all required fields, constraints, and indexes", "dependencies": [], "details": "Create migration file `migrations/20250709134932_add_org_phone_lines_table.sql` with: CREATE TABLE org_phone_lines with SERIAL PRIMARY KEY id, org_id INTEGER REFERENCES orgs(id) ON DELETE CASCADE, line_type VARCHAR(50) NOT NULL DEFAULT 'general', external_forwarding_number VARCHAR(20), twilio_receiving_number VARCHAR(20), twilio_number_sid VARCHAR(100), twiml_app_sid VARCHAR(100), is_primary_line BOOLEAN DEFAULT false, created_at TIMESTAMPTZ DEFAULT NOW(), updated_at TIMESTAMPTZ DEFAULT NOW(). Add UNIQUE constraints on twilio_receiving_number and (org_id, external_forwarding_number). Create indexes on twilio_receiving_number, org_id, (org_id, line_type), and (org_id, is_primary_line) for performance optimization.\n<info added on 2025-08-18T21:49:11.917Z>\nImplementation plan:\n- Create new migration file: `migrations/20250819090000_add_org_phone_lines_table.sql` (via `make db-new` / goose create) with Up/Down blocks.\n- Up: `CREATE TABLE org_phone_lines (...)` with:\n  - id SERIAL PRIMARY KEY\n  - org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE\n  - line_type TEXT NOT NULL DEFAULT 'general'\n  - external_forwarding_number TEXT\n  - twilio_receiving_number TEXT\n  - twilio_number_sid TEXT\n  - twiml_app_sid TEXT\n  - is_primary_line BOOLEAN NOT NULL DEFAULT FALSE\n  - created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()\n  - updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()\n  - Constraints:\n    - UNIQUE (twilio_receiving_number)\n    - UNIQUE (org_id, external_forwarding_number)\n  - Indexes:\n    - CREATE INDEX idx_org_phone_lines_twilio_receiving_number ON org_phone_lines(twilio_receiving_number)\n    - CREATE INDEX idx_org_phone_lines_org_id ON org_phone_lines(org_id)\n    - CREATE INDEX idx_org_phone_lines_org_id_line_type ON org_phone_lines(org_id, line_type)\n    - CREATE INDEX idx_org_phone_lines_org_id_is_primary ON org_phone_lines(org_id, is_primary_line)\n- Down: Drop indexes, policies (if any created in this file), then DROP TABLE IF EXISTS org_phone_lines.\n- Notes:\n  - Keep twilio_receiving_number nullable initially to support gradual backfill; we can tighten later if needed.\n  - Use plain CREATEs (not IF NOT EXISTS) so Goose properly fails on drift; idempotency handled by Down.\n</info added on 2025-08-18T21:49:11.917Z>", "status": "done", "testStrategy": "Verify table creation with correct schema, validate all constraints prevent duplicate entries, test indexes improve query performance, confirm foreign key relationships work correctly"}, {"id": 2, "title": "Implement Row Level Security policies", "description": "Add RLS policies to the org_phone_lines table following Hero Core security patterns", "dependencies": ["13.1"], "details": "Enable RLS on org_phone_lines table with ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY. Create org_phone_lines_organization_access policy allowing users to access phone lines for their organization using org_id matching current user's org context. Create org_phone_lines_meta_org_access policy for meta-organization access patterns. Follow existing Hero Core RLS policy patterns from other tables, ensuring proper security isolation between organizations while allowing necessary administrative access.\n<info added on 2025-08-18T21:49:17.811Z>\nImplementation plan details:\n- Execute RLS enablement and policy creation in the same migration file after table creation to maintain consistency with existing Hero Core patterns (following call_forward_events table approach)\n- Use exact SQL commands: ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY followed by CREATE POLICY statements\n- org_phone_lines_organization_access policy: USING (org_id::text = current_setting('app.org_id')) WITH CHECK (org_id::text = current_setting('app.org_id')) for standard organization access\n- org_phone_lines_meta_org_access policy: USING (current_setting('app.org_id') = '-1') WITH CHECK (current_setting('app.org_id') = '-1') for meta-organization administrative access\n- Migration rollback: Include DROP POLICY IF EXISTS statements for both policies in down migration (RLS automatically disabled when table is dropped)\n- Testing requirements: Verify read/write operations work correctly after setting app.org_id context variable, test both organization-level and meta-organization access patterns\n</info added on 2025-08-18T21:49:17.811Z>", "status": "done", "testStrategy": "Test RLS policies with different org contexts, verify users can only access their organization's phone lines, validate meta-org access works correctly, test policy enforcement on all CRUD operations"}, {"id": 3, "title": "Migrate existing phone line data from orgs table", "description": "Execute data migration to transfer existing phone line data from the orgs table to the new org_phone_lines table", "dependencies": ["13.2"], "details": "Create INSERT INTO org_phone_lines SELECT statement to migrate data from orgs table where phone line fields are not null. Map orgs.twilio_number to twilio_receiving_number, orgs.twilio_number_sid to twilio_number_sid, orgs.twiml_app_sid to twiml_app_sid, orgs.forwarded_from_number to external_forwarding_number. Set line_type to 'general' and is_primary_line to true for all migrated records. Handle NULL values appropriately and ensure data integrity during migration. Add validation queries to confirm all data was migrated correctly.\n<info added on 2025-08-18T21:49:22.310Z>\nImplementation plan:\n- Backfill existing data from `orgs` to `org_phone_lines` with a single INSERT…SELECT:\n```\nINSERT INTO org_phone_lines (\n  org_id, line_type, external_forwarding_number, twilio_receiving_number,\n  twilio_number_sid, twiml_app_sid, is_primary_line\n)\nSELECT\n  o.id,\n  'general' AS line_type,\n  NULLIF(o.forwarded_from_number, '') AS external_forwarding_number,\n  NULLIF(o.twilio_number, '') AS twilio_receiving_number,\n  NULLIF(o.twilio_number_sid, '') AS twilio_number_sid,\n  NULLIF(o.twiml_app_sid, '') AS twiml_app_sid,\n  TRUE AS is_primary_line\nFROM orgs o\nWHERE (o.twilio_number IS NOT NULL AND o.twilio_number <> '')\n   OR (o.forwarded_from_number IS NOT NULL AND o.forwarded_from_number <> '');\n```\n- Post-backfill validation:\n  - Compare counts where `orgs.twilio_number` not null vs inserted rows.\n  - Spot check a few orgs.\n- Do NOT add NOT NULL constraints yet; leave flexibility for orgs without numbers.\n- Down: DELETE FROM org_phone_lines where created via backfill (heuristic: those with is_primary_line=true AND line_type='general').\n</info added on 2025-08-18T21:49:22.310Z>", "status": "done", "testStrategy": "Confirm data migration preserves all existing phone line data without loss, validate field mappings are correct, test that migrated records have proper line_type and is_primary_line values, verify no data corruption occurred"}, {"id": 4, "title": "Clean up orgs table by removing migrated columns", "description": "Remove the phone line related columns from the orgs table after successful data migration", "dependencies": ["13.3"], "details": "Execute ALTER TABLE orgs DROP COLUMN statements to remove: twilio_number, twilio_number_sid, twiml_app_sid, forwarded_from_number, call_forwarding_type, is_call_forwarding_enabled, sip_uri. Ensure all dependent views, functions, or triggers are updated before dropping columns. Add rollback instructions in case migration needs to be reversed. Verify no application code references these columns before removal.\n<info added on 2025-08-18T21:49:25.865Z>\nImplementation plan:\n- Defer column drops until after Tasks 14–17 are complete and services are switched to use `org_phone_lines`.\n- Prepare a separate migration for cleanup later:\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number_sid;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS twiml_app_sid;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS forwarded_from_number;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS call_forwarding_type;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS is_call_forwarding_enabled;\n  - ALTER TABLE orgs DROP COLUMN IF EXISTS sip_uri;\n- Include a reversible Down block that re-adds columns (nullable) to allow rollback.\n- Preconditions: ensure no application code reads these columns (grep and remove usages), deploy API changes, and run smoke tests before dropping.\n</info added on 2025-08-18T21:49:25.865Z>", "status": "done", "testStrategy": "Validate cleanup removes only intended columns from orgs table, test that no dependent database objects are broken, verify application continues to function after column removal, confirm rollback procedures work if needed"}]}, {"id": 14, "title": "Protocol Buffer Definitions and Code Generation", "description": "Update the orgs.proto file with OrgPhoneLine message definitions and all CRUD service methods, then regenerate Go and TypeScript bindings", "details": "Update `lib/proto/hero/orgs/v1/orgs.proto` with: OrgPhoneLine message containing all fields (id, org_id, line_type, external_forwarding_number, twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, created_at, updated_at). Add service methods to OrgsService: CreateOrgPhoneLine, GetOrgPhoneLineByTwilioNumber, ListOrgPhoneLines, UpdateOrgPhoneLine, DeleteOrgPhoneLine, SetPrimaryOrgPhoneLine. Define all corresponding request/response message types with proper field definitions and validation. Use google.protobuf.Timestamp for timestamp fields. Regenerate Go bindings for services and TypeScript bindings for frontend integration.", "testStrategy": "Validate proto file compiles without errors, verify all message fields have correct types and numbers, test generated Go code compiles and integrates with existing codebase, confirm TypeScript bindings generate correctly, validate service method signatures match requirements", "priority": "high", "dependencies": [13], "status": "done", "subtasks": [{"id": 1, "title": "Define OrgPhoneLine Message and Request/Response Types", "description": "Update the orgs.proto file to define the OrgPhoneLine message with all required fields and create all necessary request/response message types for CRUD operations", "dependencies": [], "details": "In `lib/proto/hero/orgs/v1/orgs.proto`, define the OrgPhoneLine message with fields: string id, string org_id, string line_type, string external_forwarding_number, string twilio_receiving_number, string twilio_number_sid, string twiml_app_sid, bool is_primary_line, google.protobuf.Timestamp created_at, google.protobuf.Timestamp updated_at. Create request/response message types: CreateOrgPhoneLineRequest/Response, GetOrgPhoneLineByTwilioNumberRequest/Response, ListOrgPhoneLinesRequest/Response, UpdateOrgPhoneLineRequest/Response, DeleteOrgPhoneLineRequest/Response, SetPrimaryOrgPhoneLineRequest/Response. Use proper field numbering and include validation annotations where appropriate.", "status": "done", "testStrategy": "Validate proto file syntax by running protoc compiler, verify all message fields have unique field numbers, confirm proper use of google.protobuf.Timestamp imports"}, {"id": 2, "title": "Add CRUD Service Methods to OrgsService", "description": "Extend the OrgsService in the proto file with all required phone line management service methods", "dependencies": ["14.1"], "details": "In the existing OrgsService definition within `lib/proto/hero/orgs/v1/orgs.proto`, add the following RPC methods: CreateOrgPhoneLine(CreateOrgPhoneLineRequest) returns (CreateOrgPhoneLineResponse), GetOrgPhoneLineByTwilioNumber(GetOrgPhoneLineByTwilioNumberRequest) returns (GetOrgPhoneLineByTwilioNumberResponse), ListOrgPhoneLines(ListOrgPhoneLinesRequest) returns (ListOrgPhoneLinesResponse), UpdateOrgPhoneLine(UpdateOrgPhoneLineRequest) returns (UpdateOrgPhoneLineResponse), DeleteOrgPhoneLine(DeleteOrgPhoneLineRequest) returns (DeleteOrgPhoneLineResponse), SetPrimaryOrgPhoneLine(SetPrimaryOrgPhoneLineRequest) returns (SetPrimaryOrgPhoneLineResponse). Ensure each method uses the corresponding request/response types defined in the previous subtask.", "status": "done", "testStrategy": "Verify proto file compiles without errors after adding service methods, validate all RPC methods reference correct request/response message types, confirm service method signatures match requirements"}, {"id": 3, "title": "Regenerate Go and TypeScript Bindings", "description": "Generate updated Go and TypeScript code bindings from the modified proto file to enable integration with backend services and frontend applications", "dependencies": ["14.2"], "details": "Run the proto code generation pipeline to create updated bindings. For Go bindings, use protoc with go_out and grpc_out plugins to generate service interfaces and message structs that can be imported by the orgs service implementation. For TypeScript bindings, use protoc with ts_proto or similar plugin to generate TypeScript definitions and client stubs for frontend integration. Ensure the generated code includes all new OrgPhoneLine message types and service method signatures. Update any import paths or package references as needed to maintain compatibility with existing codebase structure.", "status": "done", "testStrategy": "Verify generated Go code compiles without errors and integrates with existing service structure, confirm TypeScript bindings generate correctly with proper type definitions, test that generated service interfaces match the proto definitions, validate import paths work correctly in both Go and TypeScript environments"}]}, {"id": 15, "title": "Orgs Service Repository Layer Implementation", "description": "Implement all database operations for phone line management in the PostgreSQL repository layer with proper error handling and transaction management", "details": "Update `services/orgs/internal/data/postgres_orgs_repo.go` with complete CRUD operations: CreateOrgPhoneLine() with SQL INSERT and proper error handling for unique constraint violations, GetOrgPhoneLineByTwilioNumber() with optimized query using twilio_receiving_number index, ListOrgPhoneLines() with org_id filtering and optional line_type filtering, UpdateOrgPhoneLine() with SQL UPDATE and optimistic locking, DeleteOrgPhoneLine() with CASCADE handling, SetPrimaryOrgPhoneLine() with transaction to unset other primary lines and set new one. Implement proper SQL transactions for data consistency, comprehensive error handling with specific error types, row-level security integration, and connection pooling optimization. Add helper methods for validation and data transformation.", "testStrategy": "Unit tests for each repository method with mock database, integration tests with real PostgreSQL instance, test error scenarios including constraint violations and concurrent access, validate transaction rollback on failures, test RLS policy enforcement, performance testing for query optimization", "priority": "high", "dependencies": [14], "status": "deferred", "subtasks": []}, {"id": 16, "title": "Orgs Service Business Logic Implementation", "description": "Implement the use case layer with business logic validation, line type management, and primary line handling in the orgs service", "details": "Update `services/orgs/internal/usecase/orgs_usecase.go` with business logic for all phone line operations. Implement ValidateLineType() function supporting 'general', 'emergency', 'parking', 'dispatch', 'admin' types. Add CreateOrgPhoneLine() with organization existence validation, line type validation, and Twilio number uniqueness checks. Implement UpdateOrgPhoneLine() with proper authorization and validation. Add SetPrimaryOrgPhoneLine() with logic to ensure only one primary line per organization. Implement DeleteOrgPhoneLine() with primary line protection (cannot delete if it's the only line). Add ListOrgPhoneLines() with proper filtering and sorting. Include comprehensive error handling with business-specific error messages and proper HTTP status code mapping.", "testStrategy": "Unit tests for all business logic methods, test line type validation with valid and invalid inputs, test primary line management scenarios, validate organization authorization checks, test edge cases like deleting last remaining line, integration tests with repository layer", "priority": "medium", "dependencies": [15], "status": "deferred", "subtasks": []}, {"id": 17, "title": "Bootstrap Script Updates for Multi-Line Support", "description": "Update organization bootstrap scripts to create phone lines through the new API endpoints instead of direct database insertion", "details": "Update `bootstrap/config/recipes/org_bootstrap_local.yaml` to use the new phone line creation workflow. Remove phone line fields from organization creation payload (twilio_number, twilio_number_sid, twiml_app_sid). Add new step 'create_primary_phone_line' that calls hero.orgs.v1.OrgsService/CreateOrgPhoneLine with org_id from previous step, line_type='general', twilio_receiving_number='+***********', twiml_app_sid='AP367659bb9684ba033a80685ee537ffcc', is_primary_line=true. Update dependencies to ensure phone line creation happens after organization creation. Test with local development environment to ensure bootstrap process works end-to-end. Update any other bootstrap recipes that create organizations with phone lines.", "testStrategy": "Test bootstrap process in local development environment, verify organizations are created successfully with primary phone lines, validate phone line data is correctly populated, test bootstrap rollback procedures, confirm existing bootstrap data remains compatible", "priority": "medium", "dependencies": [16], "status": "deferred", "subtasks": []}, {"id": 18, "title": "Call Queue Schema Integration", "description": "Update the call_queue table to reference phone lines and backfill existing call data with primary line associations", "details": "Create new migration to add org_phone_line_id column to call_queue table with foreign key constraint to org_phone_lines(id). Add index on org_phone_line_id for query performance: `CREATE INDEX idx_call_queue_org_phone_line_id ON call_queue(org_phone_line_id)`. Implement backfill query to associate existing calls with their organization's primary phone line: `UPDATE call_queue SET org_phone_line_id = (SELECT id FROM org_phone_lines WHERE org_phone_lines.org_id = call_queue.org_id AND is_primary_line = true)`. Add NOT NULL constraint after backfill is complete. Update any existing queries in communications service that reference call_queue to handle the new phone line relationship. Ensure proper cascade behavior when phone lines are deleted.", "testStrategy": "Test migration on staging data to ensure no data loss, validate foreign key constraints work correctly, test backfill query performance and accuracy, verify all existing calls get associated with correct primary lines, test cascade deletion behavior, validate index improves query performance", "priority": "high", "dependencies": [17], "status": "deferred", "subtasks": [{"id": 1, "title": "Create Database Migration for org_phone_line_id Column", "description": "Create and execute a database migration to add the org_phone_line_id column to the call_queue table with proper foreign key constraints and indexing", "dependencies": [], "details": "Create a new migration file that adds org_phone_line_id column to call_queue table as INTEGER type, initially nullable. Add foreign key constraint referencing org_phone_lines(id) with ON DELETE CASCADE behavior. Create performance index: CREATE INDEX idx_call_queue_org_phone_line_id ON call_queue(org_phone_line_id). Test the migration on a copy of production data to ensure no issues with existing records.", "status": "pending", "testStrategy": "Test migration rollback functionality, verify foreign key constraints work correctly, validate index creation improves query performance, test on staging environment with production data volume"}, {"id": 2, "title": "Implement Data Backfill for Existing Call Records", "description": "Execute a backfill operation to associate all existing call_queue records with their organization's primary phone line", "dependencies": ["18.1"], "details": "Implement and execute the backfill query: UPDATE call_queue SET org_phone_line_id = (SELECT id FROM org_phone_lines WHERE org_phone_lines.org_id = call_queue.org_id AND is_primary_line = true) WHERE org_phone_line_id IS NULL. Monitor query performance and consider batching for large datasets. Verify all records are successfully updated and no calls are left without phone line associations. After successful backfill, add NOT NULL constraint to org_phone_line_id column.", "status": "pending", "testStrategy": "Validate backfill query accuracy by spot-checking random samples, verify performance on large datasets, confirm all existing calls get associated with correct primary lines, test constraint addition after backfill completion"}, {"id": 3, "title": "Update Communications Service Queries and Cascade Behavior", "description": "Update all existing queries in the communications service to handle the new phone line relationship and ensure proper cascade deletion behavior", "dependencies": ["18.2"], "details": "Identify and update all queries in communications service that reference call_queue table to include org_phone_line_id joins where appropriate. Update call creation logic to require org_phone_line_id parameter. Modify call retrieval queries to include phone line information. Test cascade deletion behavior when phone lines are deleted to ensure call_queue records are properly handled. Update any ORM models or query builders to reflect the new schema relationship.", "status": "pending", "testStrategy": "Test all updated queries for performance and correctness, verify cascade deletion works as expected without orphaning records, validate call creation and retrieval functions work with new schema, run integration tests to ensure no breaking changes in communications service API"}]}, {"id": 19, "title": "Communications Service Call Queue Repository Updates", "description": "Update call queue repository methods to capture and preserve phone line context throughout the call lifecycle", "details": "Update `services/communications/internal/cellularcall/data/postgres_callqueue_repo.go` to include org_phone_line_id in all call creation and query methods. Modify CreateCall() method to accept and store phone line ID. Update GetCallsByOrg() and similar query methods to JOIN with org_phone_lines table and return phone line information. Add GetCallsByPhoneLine() method for line-specific call filtering. Update webhook handling methods to identify which phone line received the call based on Twilio receiving number. Modify call status update methods to preserve phone line context. Add helper methods for phone line lookup by Twilio number. Ensure all database queries use proper indexes for performance.", "testStrategy": "Unit tests for updated repository methods, integration tests with real database, test call creation with phone line context, validate query methods return correct phone line information, test webhook handling identifies correct phone lines, performance testing for JOIN queries, test backward compatibility with existing calls", "priority": "medium", "dependencies": [18], "status": "deferred", "subtasks": []}, {"id": 20, "title": "Webhook Handler and Call Routing Updates", "description": "Update Twilio webhook handlers to identify receiving phone lines and modify call creation logic to preserve line context throughout the call lifecycle", "details": "Update Twilio webhook handlers to extract the 'To' number from incoming webhooks and use GetOrgPhoneLineByTwilioNumber() to identify which phone line received the call. Modify call creation logic to include org_phone_line_id when creating new call records. Update call routing logic to consider line type for specialized handling (emergency vs general calls). Ensure webhook responses include phone line context for proper call flow continuation. Add logging to track which phone line handled each call for debugging and analytics. Update error handling to gracefully handle cases where phone line lookup fails. Maintain backward compatibility for existing single-line setups by defaulting to primary line when line context is missing.", "testStrategy": "End-to-end testing with multiple phone lines and Twilio webhooks, test call routing preserves line context, validate emergency vs general call handling, test webhook error scenarios, verify logging captures phone line information, test backward compatibility with existing webhook flows, integration testing with staging Twilio environment", "priority": "medium", "dependencies": [19], "status": "deferred", "subtasks": []}, {"id": 21, "title": "Frontend: Implement Call Settings Panel UI", "description": "Develop the gear icon entrypoint and modal/panel for 'Active Calls – Settings' in the Call module, including all controls and tooltips per PRD.", "details": "Implement the 'Active Calls – Settings' panel per PRD. Controls: global toggle; Assigned Dispatcher selector that couples asset id/name/number and, when changed with Override OFF, automatically sets the number to that asset’s contact_no; Override Default Number checkbox; when checked, show number dropdown of unique org numbers; Confirm/Cancel buttons. If the assigned asset has no number, show '(---) --- ----' and keep <PERSON><PERSON><PERSON> disabled until Override is enabled and a number is chosen. Disable settings button with tooltip while the viewing asset has any non-ended call.", "testStrategy": "Unit test each UI component with Jest and React Testing Library. Validate control states, tooltips, and modal behavior. Use Cypress for end-to-end tests covering entrypoint, modal open/close, control interactivity, and disabled states.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Gear Icon Entrypoint and Modal/Panel Shell", "description": "Create the gear icon button as the entrypoint for the Call Settings Panel. Implement the modal or side panel shell that opens when the gear icon is clicked, ensuring accessibility and focus management.", "dependencies": [], "details": "Use a modal or panel component (e.g., Material UI Dialog or custom modal). Ensure the modal overlays the main content, traps focus, and can be closed via UI or keyboard. The gear icon should be visible and clickable unless disabled by business logic.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Add Global Toggle Control", "description": "Implement the global toggle switch for enabling/disabling call forwarding within the settings panel, as specified in the PRD.", "dependencies": ["21.1"], "details": "The toggle should reflect and control the enabled state. Ensure proper labeling, accessibility, and state synchronization with the rest of the panel.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implement Assigned Dispatcher Selector with Asset Coupling Logic", "description": "Add the Assigned Dispatcher selector, coupling asset id/name/number. Implement logic so that when the asset is changed and Override is OFF, the number is set to the asset’s contact_no.", "dependencies": ["21.2"], "details": "Selector should display asset options with id, name, and number. On change, update the number field automatically if Override is not enabled. Handle cases where the assigned asset has no number.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Implement Override Default Number Checkbox and Number Dropdown", "description": "Add the Override Default Number checkbox. When checked, display a dropdown of unique organization numbers for selection.", "dependencies": ["21.3"], "details": "Checkbox toggles the override state. When enabled, show a dropdown with all unique org numbers. Ensure the dropdown is only interactive when Override is checked.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Implement Confirm/Cancel Button Logic and Validation", "description": "Add Confirm and Cancel buttons with validation logic. Disable Confirm if the assigned asset has no number and Override is not enabled or no number is selected.", "dependencies": ["21.4"], "details": "Confirm should only be enabled when a valid number is selected or Override is enabled with a chosen number. Cancel closes the panel without saving. Show '(---) --- ----' placeholder if no number is available.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "Implement Disabled State and Tooltip Logic for Settings Button", "description": "Disable the gear/settings button and show a tooltip when the viewing asset has any non-ended call, per PRD requirements.", "dependencies": ["21.1"], "details": "Check asset call state before enabling the settings button. If disabled, display a tooltip explaining why. Ensure tooltip is accessible and appears on hover/focus.", "status": "pending", "testStrategy": ""}]}, {"id": 22, "title": "Frontend: State Management & API Integration", "description": "Integrate frontend with backend APIs for reading and updating call forwarding config, and manage local/global state.", "details": "Use React Query for fetching/caching. Wire UpdateForwardingConfig and GetForwardingConfig. Implement conditional polling (~3s) using ETag/Last-Modified; on 304 keep cache, on 200 diff against previous config and show toast only for meaningful changes (enabled state, assigned asset, forward_to_number), never on initial load. Maintain previous config in memory; normalize phones to E.164 before send. Store config in shared context.", "testStrategy": "Mock API responses in unit tests. Validate polling, config refresh, error handling, and optimistic updates. Use MSW for integration tests simulating backend responses.", "priority": "high", "dependencies": [21], "status": "pending", "subtasks": [{"id": 1, "title": "React Query Setup for GetForwardingConfig", "description": "Implement React Query's useQuery to fetch call forwarding configuration from the backend API, ensuring proper caching and error handling.", "dependencies": [], "details": "Set up QueryClientProvider at the app root. Create a useQuery hook for GetForwardingConfig, specifying queryKey and queryFn. Handle loading and error states appropriately.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "React Query Setup for UpdateForwardingConfig", "description": "Implement React Query's useMutation to update call forwarding configuration via backend API, with cache invalidation and optimistic updates.", "dependencies": ["22.1"], "details": "Create a useMutation hook for UpdateForwardingConfig. On success, invalidate GetForwardingConfig query to refetch updated data. Handle mutation errors and loading states.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Conditional Polling Implementation with ETag/Last-Modified", "description": "Integrate conditional polling (~3s) for GetForwardingConfig using ETag/Last-Modified headers to minimize unnecessary data fetching.", "dependencies": ["22.1"], "details": "Configure polling interval in useQuery. On each poll, send ETag/Last-Modified headers; if response is 304, retain cache; if 200, proceed to diff logic. Ensure polling does not occur on initial load.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "In-Memory Previous Config Management and Diff Logic", "description": "Maintain previous call forwarding config in memory and implement logic to diff against new config for meaningful changes.", "dependencies": ["22.1", "22.3"], "details": "Store previous config in a React ref or context. On successful fetch (200), compare enabled state, assigned asset, and forward_to_number fields. Trigger downstream logic only for meaningful changes.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Toast Notification Logic for Meaningful Changes", "description": "Show toast notifications only when meaningful changes are detected in enabled state, assigned asset, or forward_to_number, excluding initial load.", "dependencies": ["22.4"], "details": "Integrate a toast notification system (e.g., Material UI Snackbar). Ensure notifications are triggered only when diff logic detects relevant changes, and suppress notifications on initial load.", "status": "pending", "testStrategy": ""}, {"id": 6, "title": "E.164 Normalization Before API Send", "description": "Normalize phone numbers to E.164 format before sending updates to the backend API.", "dependencies": ["22.2"], "details": "Use a library (e.g., google-libphonenumber) to format phone numbers to E.164. Validate and transform numbers in the update mutation before API call.", "status": "pending", "testStrategy": ""}, {"id": 7, "title": "Shared Context Integration for Config State", "description": "Store and provide call forwarding config state via a shared React context for access across relevant components.", "dependencies": ["22.1", "22.2", "22.4"], "details": "Create a React context to hold config state and previous config. Ensure context is updated on successful fetch and mutation, and consumed by child components as needed.", "status": "pending", "testStrategy": ""}]}, {"id": 23, "title": "Backend: Extend ForwardingConfig API Validation", "description": "Ensure backend enforces validation rules for enabling/disabling call forwarding per PRD, including phone normalization and required fields.", "details": "Update hero.orgs.v1 service to validate: forward_to_number required when enabling (unless already stored); assigned_asset_id optional; normalize phone to E.164. On disable, preserve forward_to_number in DB and return latest config without nulling it. Ensure UpdateForwardingConfig upserts into org_call_forwarding_config and returns latest config.", "testStrategy": "Write unit tests for validation logic using Go’s testing package. Cover all edge cases: missing number, invalid format, disabling, and upsert behavior. Use integration tests to verify DB writes and API responses.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Validation Logic for Enabling/Disabling Forwarding", "description": "Develop backend logic to enforce PRD validation rules for enabling and disabling call forwarding, including required and optional fields.", "dependencies": [], "details": "Ensure that 'forward_to_number' is required when enabling forwarding (unless already stored), and 'assigned_asset_id' is optional. On disabling, preserve 'forward_to_number' in the database and ensure the latest config is returned without nulling the field.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Normalize Phone Numbers to E.164 Format", "description": "Integrate phone number normalization to E.164 standard for all relevant API inputs and database writes.", "dependencies": ["23.1"], "details": "Use a library (e.g., Google's libphonenumber) to parse and format phone numbers into E.164 format before validation and storage. Ensure all phone numbers in the system conform to this format.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implement Upsert Logic for org_call_forwarding_config", "description": "Ensure UpdateForwardingConfig performs an upsert operation on org_call_forwarding_config and returns the latest configuration.", "dependencies": ["23.1", "23.2"], "details": "Design the upsert logic to insert or update the forwarding config as needed, preserving existing data where required and ensuring atomicity.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Shape API Responses and Handle Errors", "description": "Define and implement API response structures, including error handling for all validation and persistence scenarios.", "dependencies": ["23.1", "23.2", "23.3"], "details": "Ensure the API returns the latest config after updates, provides clear error messages for validation failures, and does not null 'forward_to_number' on disable. Document response formats and error codes.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Develop Unit and Integration Tests for Validation Paths", "description": "Write comprehensive unit and integration tests covering all validation, normalization, upsert, and API response scenarios.", "dependencies": ["23.1", "23.2", "23.3", "23.4"], "details": "Test all edge cases: missing or invalid numbers, enabling/disabling logic, upsert behavior, and API error handling. Use Go’s testing package and include integration tests for database and API layers.", "status": "pending", "testStrategy": ""}]}, {"id": 24, "title": "Frontend: Asset Data Integration for Selectors", "description": "Integrate existing asset APIs to populate dispatcher selector and phone number dropdowns in the UI, leveraging existing useListAssets hook.", "details": "Use existing useListAssets hook from assets service to fetch organization assets. Filter and format data for UI display as '{AssetName} • ({number})' with placeholder '(---) --- ----' for missing numbers. Implement client-side deduplication for phone number dropdown.", "testStrategy": "Unit test asset data filtering and formatting logic. Integration test dropdown population with various asset data scenarios.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Integrate Existing useListAssets Hook", "description": "Use the existing useListAssets hook from assets service to fetch organization assets with contact numbers for populating UI selectors.", "dependencies": [], "details": "Import and configure the existing useListAssets hook with appropriate organization filtering. Ensure proper error handling and loading states for the asset data.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Filter and Format Asset Data for UI", "description": "Filter assets with contact numbers and format data for dropdown display as '{AssetName} • ({number})' with placeholder '(---) --- ----' for missing numbers.", "dependencies": ["24.1"], "details": "Create utility functions to filter assets and format display strings. Handle cases where contact_no is null or empty by showing placeholder text. Sort assets by name for consistent UI ordering.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implement Phone Number Deduplication Logic", "description": "Create client-side logic to deduplicate unique contact_no values per organization for the Override phone number dropdown.", "dependencies": ["24.1"], "details": "Extract unique phone numbers from asset list, removing duplicates while preserving the association with asset names. Use existing standardizeUSPhoneNumber utility for normalization before deduplication.", "status": "pending", "testStrategy": ""}], "complexityScore": 3}, {"id": 25, "title": "Frontend: Call Module Inactive State Logic", "description": "Implement per-asset logic to 'brick' the Call module UI when the asset has no active calls, per PRD requirements.", "details": "Query call_queue for asset_id and state != 'ended' via backend API. If no active calls, hide call button, remove panes, and show 'Call Module Inactive' banner and nav chip. Gear icon remains enabled for toggling off Mobile Dispatch. Use React conditional rendering and context for state management.", "testStrategy": "Unit test state transitions and UI rendering. End-to-end test with simulated call_queue data to verify correct bricking/unbricking behavior.", "priority": "high", "dependencies": [22], "status": "pending", "subtasks": [{"id": 1, "title": "Integrate Backend API for call_queue State", "description": "Implement logic to query the backend API for call_queue data, filtering by asset_id and state != 'ended', to determine if there are any active calls for the asset.", "dependencies": [], "details": "Use React Query or equivalent data-fetching mechanism to poll the backend for call_queue status. Ensure efficient polling and error handling. Store the result in context or component state for downstream UI logic.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implement Conditional Rendering for Inactive State", "description": "Develop React conditional rendering logic to detect when an asset has no active calls and trigger the inactive state UI.", "dependencies": ["25.1"], "details": "Use context or props to pass call_queue state. Apply conditional rendering patterns (if/else, ternary, switch, etc.) to determine when to 'brick' the Call module UI based on the absence of active calls.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Update UI to Hide/Remove Controls in Inactive State", "description": "Modify the UI to hide the call button, remove call panes, and ensure the gear icon remains enabled when the Call module is inactive.", "dependencies": ["25.2"], "details": "Refactor component structure so that controls are only rendered when the module is active. Ensure the gear icon is always accessible for toggling Mobile Dispatch, regardless of call state.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Display 'Call Module Inactive' Banner and Nav Chip", "description": "Implement logic to show the 'Call Module Inactive' banner and navigation chip when there are no active calls for the asset.", "dependencies": ["25.2"], "details": "Add banner and nav chip components that are conditionally rendered based on the inactive state. Ensure correct styling and placement per PRD requirements.", "status": "pending", "testStrategy": ""}, {"id": 5, "title": "Develop Unit and End-to-End Tests for State Transitions", "description": "Write comprehensive unit and E2E tests to verify correct UI state transitions and rendering for both active and inactive call scenarios.", "dependencies": ["25.1", "25.2", "25.3", "25.4"], "details": "Test all state transitions, including bricking and unbricking the module, UI updates, and banner/nav chip display. Use mocked API responses and simulate user interactions to ensure robust coverage.", "status": "pending", "testStrategy": ""}]}, {"id": 26, "title": "Frontend: Confirmation Dialogs & Error <PERSON>", "description": "Implement confirmation dialogs for disabling Mobile Dispatch and toggling Override, and robust inline error handling for API failures.", "details": "Use Material UI Dialog components for confirmations. Show dialog when disabling Mobile Dispatch (with targeted asset/number) and when turning off Override with a custom number. Inline error display for failed API calls with retry option. Ensure dialogs block destructive actions until confirmed.", "testStrategy": "Unit test dialog open/close logic and error display. End-to-end test disabling flows and error scenarios with Cypress.", "priority": "medium", "dependencies": [21, 22], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Confirmation Dialog for Disabling Mobile Dispatch", "description": "Create a Material UI confirmation dialog that appears when a user attempts to disable Mobile Dispatch. The dialog should display the targeted asset and/or number, block the action until confirmed, and handle cancel/confirm flows.", "dependencies": [], "details": "Use Material UI Dialog components. Ensure the dialog interrupts the flow and requires explicit user confirmation before proceeding. Integrate with the relevant state and API logic to block destructive actions until confirmed.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implement Confirmation Dialog for Toggling Override", "description": "Develop a Material UI confirmation dialog for toggling the Override setting, specifically when turning off Override with a custom number. The dialog should require user confirmation before committing the change.", "dependencies": ["26.1"], "details": "Utilize Material UI Dialog components. Display relevant information about the custom number being affected. Ensure the dialog blocks the action until the user confirms or cancels.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Implement Inline Error Handling and Retry Logic for API Failures", "description": "Add robust inline error handling for failed API calls related to Mobile Dispatch and Override actions. Display error messages inline and provide a retry option for users.", "dependencies": ["26.1", "26.2"], "details": "Show error messages directly in the UI near the affected controls. Implement a retry button that re-attempts the failed API call. Ensure errors are clearly communicated and do not block unrelated actions.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Develop Test Coverage for Dialog Flows and Error Scenarios", "description": "Create unit and end-to-end tests to validate dialog open/close logic, confirmation flows, error display, and retry functionality for all dialog and error scenarios.", "dependencies": ["26.1", "26.2", "26.3"], "details": "Use Jest and React Testing Library for unit tests. Use Cypress for end-to-end tests covering dialog interactions, error handling, and retry logic. Ensure all edge cases and user flows are tested.", "status": "pending", "testStrategy": ""}]}, {"id": 27, "title": "Backend: Observability & Logging", "description": "Add logging for config changes and client metrics for toggles and time-in-state per non-functional requirements.", "details": "Instrument hero.orgs.v1: log org_id, updated_by_asset_id, old→new diff (enabled flag, assigned_asset_id, forward_to_number), and timestamps. Add Prometheus counters for enable/disable, override on/off, dispatcher changed; gauge/histogram for time-in-state if trivial.", "testStrategy": "Unit test logging hooks and metric increments. Integration test log output and Prometheus scrape endpoints.", "priority": "medium", "dependencies": [23], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Logging for Config Changes", "description": "Add structured logging to hero.orgs.v1 for configuration changes, capturing org_id, updated_by_asset_id, old→new diff (enabled flag, assigned_asset_id, forward_to_number), and timestamps. Ensure logs are machine and human-readable, include necessary context, and avoid sensitive data.", "dependencies": [], "details": "Follow best practices for structured logging (e.g., JSON or key=value), include all required metadata, and ensure logs are centralized and standardized across services. Redact or mask any sensitive information as needed.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Instrument Prometheus Metrics", "description": "Add Prometheus counters for enable/disable, override on/off, and dispatcher changed events in hero.orgs.v1. Implement gauge or histogram metrics for time-in-state if feasible.", "dependencies": ["27.1"], "details": "Expose metrics endpoints for Prometheus scraping. Ensure metrics are incremented or updated at appropriate points in the codebase and follow naming conventions for clarity and consistency.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Test Coverage for Logs and Metrics", "description": "Develop unit and integration tests to verify correct logging of config changes and accurate Prometheus metric increments and values.", "dependencies": ["27.1", "27.2"], "details": "Unit tests should validate that logs contain all required fields and that metrics are updated as expected. Integration tests should check log output and Prometheus scrape endpoints for correctness and completeness.", "status": "pending", "testStrategy": ""}]}, {"id": 28, "title": "Frontend: Phone Number Formatting & Display Integration", "description": "Integrate existing phone number utilities for UI display and validation, leveraging standardizeUSPhoneNumber and formatPhoneNumberForDisplay functions.", "details": "Use existing standardizeUSPhoneNumber and formatPhoneNumberForDisplay utilities from caller-identification.ts. Display numbers as '{AssetName} • ({number})' or '(---) --- ----' if missing. Validate formatting after save and on dropdown selection using existing utilities.", "testStrategy": "Unit test integration with existing utilities and display logic. Integration test with various asset data to verify correct rendering and placeholder usage.", "priority": "medium", "dependencies": [21, 24], "status": "pending", "subtasks": [{"id": 1, "title": "Integrate Existing Phone Number Utilities", "description": "Import and use existing standardizeUSPhoneNumber and formatPhoneNumberForDisplay functions from caller-identification.ts for phone number normalization and display formatting.", "dependencies": [], "details": "Import the existing utilities and integrate them into the call forwarding UI components. Use standardizeUSPhoneNumber for E.164 normalization before API calls and formatPhoneNumberForDisplay for UI rendering.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implement UI Display Logic with Existing Utilities", "description": "Develop UI logic to display phone numbers using existing formatting utilities in the format '{AssetName} • ({number})' when present, or '(---) --- ----' as a placeholder when missing.", "dependencies": ["28.1"], "details": "Update relevant UI components to use the existing formatting utilities. Ensure placeholders are shown for missing numbers and that the dropdown enforces selection from valid, formatted numbers only.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Create Test Coverage for Utility Integration", "description": "Write unit and integration tests to verify correct integration with existing phone utilities, placeholder rendering, and validation logic across all relevant UI states.", "dependencies": ["28.1", "28.2"], "details": "Develop tests for integration with existing utilities, including valid numbers, missing numbers, and edge cases. Ensure tests cover both utility integration and UI rendering, including dropdown selection and save validation.", "status": "pending", "testStrategy": ""}], "complexityScore": 2}, {"id": 29, "title": "Backend: Real-Time Sync & Polling Support", "description": "Implement conditional polling support for GetForwardingConfig with ETag/Last-Modified and 304/200 responses for efficient client sync.", "details": "Extend GetForwardingConfig to include ETag (e.g., hash or updated_at) and Last-Modified headers. If If-None-Match/If-Modified-Since match current state, return 304; otherwise 200 with config. Define 'meaningful change' fields (enabled, assigned_asset_id, forward_to_number) for client toast logic. Document header usage and recommended ~3s polling interval.", "testStrategy": "Unit test header logic and response codes. Integration test polling behavior and config change detection.", "priority": "medium", "dependencies": [23], "status": "pending", "subtasks": [{"id": 1, "title": "Implement ETag and Last-Modified Header Generation", "description": "Extend GetForwardingConfig to generate and include ETag (e.g., hash or updated_at) and Last-Modified headers in the response, ensuring they accurately reflect the current state of meaningful change fields.", "dependencies": [], "details": "Determine the appropriate method for generating ETag (such as hashing enabled, assigned_asset_id, forward_to_number) and set Last-Modified based on the latest update timestamp. Ensure headers conform to HTTP standards.", "status": "pending", "testStrategy": ""}, {"id": 2, "title": "Implement Conditional 304/200 Response Logic", "description": "Add logic to GetForwardingConfig to evaluate If-None-Match and If-Modified-Since request headers and return 304 Not Modified if both match the current resource state, otherwise return 200 OK with the config.", "dependencies": ["29.1"], "details": "Check incoming request headers against current ETag and Last-Modified values. If both match, respond with 304; otherwise, respond with 200 and the latest config payload.", "status": "pending", "testStrategy": ""}, {"id": 3, "title": "Document Header Usage and Polling Interval Recommendations", "description": "Create documentation detailing how ETag and Last-Modified headers are used, how clients should interpret 304/200 responses, and recommend a ~3s polling interval for efficient sync.", "dependencies": ["29.1", "29.2"], "details": "Include examples of request/response headers, explain the logic for meaningful change detection, and provide guidance for client polling strategies.", "status": "pending", "testStrategy": ""}, {"id": 4, "title": "Develop Test Coverage for Polling and Change Detection", "description": "Write unit and integration tests to verify correct header generation, conditional response logic, and client polling behavior, including detection of meaningful changes.", "dependencies": ["29.1", "29.2", "29.3"], "details": "Ensure tests cover scenarios for both 304 and 200 responses, header accuracy, and client-side handling of polling intervals and change notifications.", "status": "pending", "testStrategy": ""}]}], "metadata": {"created": "2025-08-07T20:42:34.939Z", "updated": "2025-08-25T20:28:53.479Z", "description": "Tasks for master context"}}}